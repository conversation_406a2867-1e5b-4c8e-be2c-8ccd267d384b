# 应用配置
APP_PORT=3010
NODE_ENV=development

# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_USERNAME=root
DB_PASSWORD=your_db_password
DB_DATABASE=argochainhub

# JWT配置
JWT_SECRET=your-super-secret-jwt-key-please-change-in-production
JWT_EXPIRATION=7d

# 邮件配置 (可选)
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USER=<EMAIL>
MAIL_PASSWORD=your_email_password
MAIL_FROM=<EMAIL>

# 火山引擎TOS对象存储配置
VOLC_ACCESS_KEY_ID=your_volc_access_key_id
VOLC_ACCESS_KEY_SECRET=your_volc_access_key_secret
TOS_REGION=cn-beijing
TOS_ENDPOINT=https://tos-s3-cn-beijing.volces.com
TOS_BUCKET=argochainhub
TOS_REQUEST_TIMEOUT=60000
TOS_CDN_DOMAIN=

# Redis配置 (可选缓存)
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=your_redis_password

# 火山引擎翻译服务配置
VOLC_TRANSLATE_REGION=cn-north-1
TRANSLATE_CACHE_TTL=86400
TRANSLATE_RATE_LIMIT=100