# 文件管理接口测试文档

## 认证说明

所有文件管理接口都需要在请求头中添加：
```
Authorization: Bearer {access_token}
```

获取用户 token：
**POST** `/api/v1/auth/login`

请求体：
```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```

---

## 1. 文件上传接口

**POST** `/api/v1/uploads`

Content-Type: `multipart/form-data`

### 请求参数

| 参数名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| `file` | File | 是 | 上传的文件 | 二进制文件 |
| `type` | string | 是 | 文件类型枚举 | "product_image" |
| `relatedId` | number | 否 | 关联ID（如产品ID、公司ID等） | 123 |

## 🚀 文件上传架构升级

### 上传流程优化

本系统采用**"官方SDK + 本地临时文件"**的标准架构：

```
前端文件 → 服务器临时存储 → TOS SDK putObjectFromFile → 自动清理临时文件
```

**优势：**
- ✅ **官方推荐方案**：使用TOS SDK的`putObjectFromFile`方法，官方支持
- ✅ **内置进度监控**：使用TOS SDK的`dataTransferStatusChange`回调
- ✅ **完整错误处理**：集成TosClientError和TosServerError处理
- ✅ **自动清理机制**：上传完成自动删除临时文件

### TOS SDK标准实现

系统使用火山引擎TOS SDK的标准方法：

```typescript
// 使用官方putObjectFromFile方法
const response = await this.tosClient.putObjectFromFile({
  bucket: this.config.bucket,
  key: key,
  filePath: filePath,
  contentType: contentType,
  meta: metadata,
  dataTransferStatusChange: (event) => {
    // TOS SDK内置进度回调
    switch (event.type) {
      case DataTransferType.Started:
        // 开始上传
        break;
      case DataTransferType.Rw:
        // 上传进度
        const percent = (event.consumedBytes / event.totalBytes) * 100;
        break;
      case DataTransferType.Succeed:
        // 上传成功
        break;
    }
  },
});
```

**进度事件类型：**
- `started` - 开始上传
- `progress` - 上传进行中  
- `completed` - 上传完成
- `failed` - 上传失败

### 文件类型枚举值

| 枚举值 | 说明 | 用途 |
|--------|------|------|
| `product_image` | 产品图片 | 产品展示图片 |
| `company_certificate` | 企业证书 | 企业资质证书、营业执照等 |
| `sample_document` | 样品文档 | 样品相关文档 |
| `registration_document` | 注册文档 | 注册相关文档 |
| `other` | 其他 | 其他类型文件 |

### 支持的文件格式

**图片格式：**
- JPG/JPEG
- PNG
- GIF
- BMP
- WEBP

**文档格式：**
- PDF
- DOC/DOCX
- XLS/XLSX
- PPT/PPTX
- TXT

### 文件大小限制

- 最大文件大小：10MB
- 建议图片大小：< 5MB
- 建议文档大小：< 8MB

### 请求示例

```bash
curl -X POST "http://localhost:3010/api/v1/uploads" \
  -H "Authorization: Bearer {access_token}" \
  -F "file=@/path/to/your/image.jpg" \
  -F "type=product_image" \
  -F "relatedId=123"
```

### 响应示例

**成功响应 (201)：**
```json
{
  "id": 1,
  "filename": "1752407180518-868.jpeg",
  "originalName": "20250708-170451.jpeg",
  "mimetype": "image/jpeg",
  "size": 498508,
  "storageKey": "product_image/1/1752407180518-868.jpeg",
  "url": "https://argochainhub.tos-cn-shanghai.volces.com/product_image/1/1752407180518-868.jpeg",
  "type": "product_image",
  "relatedId": 123,
  "uploadedById": 1,
  "createdAt": "2025-01-13T12:00:00+08:00",
  "updatedAt": "2025-01-13T12:00:00+08:00"
}
```

**URL访问说明：**
- 返回的`url`字段为完整的HTTPS访问链接
- 前端可直接使用该URL显示图片或下载文件
- URL格式：`https://{bucket}.{endpoint}/{key}`
- 支持CDN加速（如配置CDN域名）

**错误响应 (400)：**
```json
{
  "message": "文件格式不支持，仅支持图片和文档格式",
  "error": "Bad Request",
  "statusCode": 400
}
```

---

## 2. 获取我的文件列表

**GET** `/api/v1/uploads/my-files?page=1&limit=20&type=product_image`

### 查询参数

| 参数名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| `page` | number | 否 | 页码，默认1 | 1 |
| `limit` | number | 否 | 每页条数，默认20 | 20 |
| `type` | string | 否 | 文件类型筛选 | "product_image" |

### 请求示例

```bash
curl -X GET "http://localhost:3010/api/v1/uploads/my-files?page=1&limit=10&type=product_image" \
  -H "Authorization: Bearer {access_token}"
```

### 响应示例

```json
{
  "data": [
    {
      "id": 1,
      "filename": "1736789123456-001.jpg",
      "originalName": "product_photo.jpg",
      "mimetype": "image/jpeg",
      "size": 2048576,
      "storageKey": "product_image/1/1736789123456-001.jpg",
      "url": "https://cdn.example.com/product_image/1/1736789123456-001.jpg",
      "type": "product_image",
      "relatedId": 123,
      "uploadedById": 1,
      "createdAt": "2025-01-13T12:00:00+08:00",
      "updatedAt": "2025-01-13T12:00:00+08:00"
    }
  ],
  "meta": {
    "totalItems": 50,
    "itemCount": 1,
    "itemsPerPage": 10,
    "totalPages": 5,
    "currentPage": 1
  }
}
```

---

## 3. 根据类型获取文件列表

**GET** `/api/v1/uploads/by-type/{type}?page=1&limit=20&relatedId=123`

### 路径参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| `type` | string | 是 | 文件类型枚举值 |

### 查询参数

| 参数名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| `page` | number | 否 | 页码，默认1 | 1 |
| `limit` | number | 否 | 每页条数，默认20 | 20 |
| `relatedId` | number | 否 | 关联ID筛选 | 123 |

### 权限说明

- **企业证书 (company_certificate)**：只能查看自己公司的文件
- **产品图片 (product_image)**：需要验证产品所有权
- **其他类型**：根据业务逻辑判断权限

### 请求示例

```bash
curl -X GET "http://localhost:3010/api/v1/uploads/by-type/product_image?relatedId=123" \
  -H "Authorization: Bearer {access_token}"
```

### 响应示例

```json
{
  "data": [
    {
      "id": 1,
      "filename": "1736789123456-001.jpg",
      "originalName": "product_photo.jpg",
      "mimetype": "image/jpeg",
      "size": 2048576,
      "url": "https://cdn.example.com/product_image/1/1736789123456-001.jpg",
      "type": "product_image",
      "relatedId": 123,
      "uploadedBy": {
        "id": 1,
        "name": "张三",
        "email": "<EMAIL>"
      },
      "createdAt": "2025-01-13T12:00:00+08:00"
    }
  ],
  "meta": {
    "totalItems": 5,
    "itemCount": 1,
    "itemsPerPage": 20,
    "totalPages": 1,
    "currentPage": 1
  }
}
```

---

## 4. 获取文件信息

**GET** `/api/v1/uploads/{id}`

### 路径参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| `id` | number | 是 | 文件ID |

### 请求示例

```bash
curl -X GET "http://localhost:3010/api/v1/uploads/1" \
  -H "Authorization: Bearer {access_token}"
```

### 响应示例

```json
{
  "id": 1,
  "filename": "1736789123456-001.jpg",
  "originalName": "product_photo.jpg",
  "mimetype": "image/jpeg",
  "size": 2048576,
  "storageKey": "product_image/1/1736789123456-001.jpg",
  "url": "https://cdn.example.com/product_image/1/1736789123456-001.jpg",
  "type": "product_image",
  "relatedId": 123,
  "uploadedById": 1,
  "uploadedBy": {
    "id": 1,
    "name": "张三",
    "email": "<EMAIL>"
  },
  "createdAt": "2025-01-13T12:00:00+08:00",
  "updatedAt": "2025-01-13T12:00:00+08:00"
}
```

---

## 5. 获取文件访问URL

**GET** `/api/v1/uploads/{id}/url`

### 用途

- 获取文件的访问URL
- 用于在前端显示文件链接
- 可能返回CDN加速后的URL

### 请求示例

```bash
curl -X GET "http://localhost:3010/api/v1/uploads/1/url" \
  -H "Authorization: Bearer {access_token}"
```

### 响应示例

```json
{
  "id": 1,
  "url": "https://cdn.example.com/product_image/1/1736789123456-001.jpg",
  "filename": "1736789123456-001.jpg"
}
```

---

## 6. 下载文件

**GET** `/api/v1/uploads/{id}/download`

### 用途

- 下载文件到本地
- 自动设置正确的文件名和Content-Type
- 触发浏览器下载行为

### 请求示例

```bash
curl -X GET "http://localhost:3010/api/v1/uploads/1/download" \
  -H "Authorization: Bearer {access_token}" \
  -O -J
```

### 响应头

```
Content-Type: image/jpeg
Content-Disposition: attachment; filename="product_photo.jpg"
Content-Length: 2048576
```

### 响应体

文件的二进制内容

---

## 7. 预览文件（图片）

**GET** `/api/v1/uploads/{id}/preview`

### 用途

- 在线预览图片文件
- 只支持图片格式（image/*）
- 设置缓存头优化性能

### 请求示例

```bash
curl -X GET "http://localhost:3010/api/v1/uploads/1/preview" \
  -H "Authorization: Bearer {access_token}"
```

### 响应头

```
Content-Type: image/jpeg
Cache-Control: public, max-age=3600
```

### 响应体

图片的二进制内容

### 错误响应

**非图片文件 (400)：**
```json
{
  "message": "文件类型不支持预览，仅支持图片格式",
  "error": "Bad Request",
  "statusCode": 400
}
```

---

## 8. 删除文件

**DELETE** `/api/v1/uploads/{id}`

### 权限说明

- 只能删除自己上传的文件
- 删除操作会同时删除云存储中的文件和数据库记录
- 删除操作不可恢复

### 请求示例

```bash
curl -X DELETE "http://localhost:3010/api/v1/uploads/1" \
  -H "Authorization: Bearer {access_token}"
```

### 响应示例

**成功响应 (200)：**
```json
{
  "message": "文件删除成功"
}
```

**权限不足 (403)：**
```json
{
  "message": "您只能删除自己上传的文件",
  "error": "Forbidden",
  "statusCode": 403
}
```

---

## 9. 云存储架构说明

### 存储服务

- **云服务商**：火山引擎TOS (Tinder Object Storage)
- **上传方式**：TOS SDK `putObjectFromFile` 官方推荐方法
- **URL格式**：`https://{bucket}.{endpoint}/{key}`
- **CDN加速**：支持CDN域名配置

### 文件命名规则

```
产品图片示例：https://argochainhub.tos-cn-shanghai.volces.com/product_image/123/1752407180518-868.jpeg
企业证书示例：https://argochainhub.tos-cn-shanghai.volces.com/company_certificate/123/1752407180518-869.pdf
样品文档示例：https://argochainhub.tos-cn-shanghai.volces.com/sample_document/123/1752407180518-870.docx
```

**文件路径结构：**
- `{文件类型}/{用户ID}/{时间戳}-{随机数}.{扩展名}`
- 确保文件名唯一性，避免覆盖

### 安全特性

- **访问权限控制**：基于用户身份和文件类型
- **文件大小限制**：防止恶意上传大文件
- **文件格式检查**：只允许特定格式文件
- **存储隔离**：按用户和类型分目录存储

---

## 10. 错误码说明

| 状态码 | 说明 | 常见原因 |
|--------|------|----------|
| 200 | 请求成功 | - |
| 201 | 创建成功 | 文件上传成功 |
| 400 | 请求错误 | 文件格式不支持、文件过大、缺少必要参数 |
| 401 | 未认证 | Token无效或过期 |
| 403 | 权限不足 | 无文件访问或删除权限 |
| 404 | 资源不存在 | 文件不存在或已被删除 |
| 413 | 文件过大 | 超过10MB限制 |
| 415 | 媒体类型不支持 | 文件格式不在允许列表中 |
| 500 | 服务器错误 | 云存储服务异常、网络错误等 |

---

## 11. 测试用例

### 11.1 文件上传测试

**基础功能测试：**

1. **图片上传测试**
   ```bash
   # 上传产品图片
   curl -X POST "http://localhost:3010/api/v1/uploads" \
     -H "Authorization: Bearer {token}" \
     -F "file=@test_product.jpg" \
     -F "type=product_image" \
     -F "relatedId=1"
   ```

2. **文档上传测试**
   ```bash
   # 上传企业证书
   curl -X POST "http://localhost:3010/api/v1/uploads" \
     -H "Authorization: Bearer {token}" \
     -F "file=@certificate.pdf" \
     -F "type=company_certificate"
   ```

**边界条件测试：**

3. **大文件测试**
   - 测试接近10MB的文件
   - 测试超过10MB的文件（应该失败）

4. **格式测试**
   - 测试支持的格式（JPG、PNG、PDF等）
   - 测试不支持的格式（EXE、BAT等）

5. **参数验证测试**
   - 缺少file参数
   - 无效的type枚举值
   - 无效的relatedId

### 11.2 权限控制测试

**访问权限测试：**

1. **文件所有者访问**
   ```bash
   # 用户A访问自己上传的文件
   curl -X GET "http://localhost:3010/api/v1/uploads/1" \
     -H "Authorization: Bearer {user_a_token}"
   ```

2. **非所有者访问**
   ```bash
   # 用户B尝试访问用户A的文件
   curl -X GET "http://localhost:3010/api/v1/uploads/1" \
     -H "Authorization: Bearer {user_b_token}"
   ```

3. **企业文件权限**
   ```bash
   # 同企业用户访问企业证书
   curl -X GET "http://localhost:3010/api/v1/uploads/by-type/company_certificate" \
     -H "Authorization: Bearer {same_company_token}"
   ```

### 11.3 文件操作测试

**下载和预览测试：**

1. **图片预览**
   ```bash
   curl -X GET "http://localhost:3010/api/v1/uploads/1/preview" \
     -H "Authorization: Bearer {token}" \
     -o preview_image.jpg
   ```

2. **文件下载**
   ```bash
   curl -X GET "http://localhost:3010/api/v1/uploads/2/download" \
     -H "Authorization: Bearer {token}" \
     -O -J
   ```

**删除测试：**

3. **删除自己的文件**
   ```bash
   curl -X DELETE "http://localhost:3010/api/v1/uploads/1" \
     -H "Authorization: Bearer {token}"
   ```

4. **删除他人文件（应该失败）**
   ```bash
   curl -X DELETE "http://localhost:3010/api/v1/uploads/2" \
     -H "Authorization: Bearer {other_user_token}"
   ```

### 11.4 查询功能测试

1. **分页查询**
   ```bash
   curl -X GET "http://localhost:3010/api/v1/uploads/my-files?page=1&limit=5" \
     -H "Authorization: Bearer {token}"
   ```

2. **类型筛选**
   ```bash
   curl -X GET "http://localhost:3010/api/v1/uploads/my-files?type=product_image" \
     -H "Authorization: Bearer {token}"
   ```

3. **关联ID筛选**
   ```bash
   curl -X GET "http://localhost:3010/api/v1/uploads/by-type/product_image?relatedId=123" \
     -H "Authorization: Bearer {token}"
   ```

---

## 12. 性能优化建议

### 12.1 上传优化

- **客户端压缩**：图片在客户端压缩后上传
- **分片上传**：大文件考虑分片上传
- **格式转换**：自动将BMP等格式转为JPG

### 12.2 访问优化

- **CDN缓存**：静态文件通过CDN分发
- **预签名URL**：生成临时访问链接
- **缓存策略**：设置合理的缓存头

### 12.3 存储优化

- **文件压缩**：自动压缩图片文件
- **重复检测**：检测重复文件避免冗余存储
- **定期清理**：清理无关联的孤儿文件

---

## 13. 环境配置

### 开发环境

- **后端服务URL**: http://localhost:3010
- **API基础路径**: http://localhost:3010/api/v1
- **Swagger文档**: http://localhost:3010/api/docs
- **时区**: 东八区 (Asia/Shanghai)

### 云存储配置

```env
# 火山引擎TOS配置
TOS_REGION=cn-shanghai
TOS_ENDPOINT=tos-cn-shanghai.volces.com
VOLC_ACCESS_KEY_ID=your_access_key
VOLC_ACCESS_KEY_SECRET=your_secret_key
TOS_BUCKET=your_bucket_name
TOS_REQUEST_TIMEOUT=60000
# TOS_CDN_DOMAIN=https://cdn.yourdomain.com
```

**配置说明：**
- `TOS_ENDPOINT`: 不包含协议前缀，TOS SDK会自动添加https://
- `VOLC_ACCESS_KEY_ID/SECRET`: 火山引擎访问密钥
- `TOS_BUCKET`: 存储桶名称
- `TOS_CDN_DOMAIN`: 可选的CDN加速域名

---

## 14. 常见问题

### Q: 支持哪些文件格式？
A: 图片格式（JPG、PNG、GIF、BMP、WEBP）和文档格式（PDF、DOC、XLS、PPT、TXT）

### Q: 文件大小限制是多少？
A: 单个文件最大10MB，建议图片<5MB，文档<8MB

### Q: 如何查看文件上传进度？
A: 当前接口不支持进度回调，建议客户端实现上传进度显示

### Q: 删除文件后能否恢复？
A: 删除操作不可恢复，会同时删除云存储和数据库记录

### Q: 文件访问权限如何控制？
A: 基于文件类型和用户身份，企业文件在企业内共享，个人文件仅本人可访问

### Q: CDN缓存多久更新？
A: 根据CDN配置，一般为1小时，新上传文件立即可访问

### Q: 如何批量上传文件？
A: 当前需要逐个调用上传接口，建议客户端实现批量上传队列