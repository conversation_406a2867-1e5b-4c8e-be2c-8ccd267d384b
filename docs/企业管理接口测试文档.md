# 企业管理接口测试文档

## 认证说明

所有管理员接口都需要在请求头中添加：
```
Authorization: Bearer {access_token}
```

管理员登录获取 token：
**POST** `/api/v1/auth/admin/login`

请求体：
```json
{
  "username": "superadmin",
  "password": "Admin123!"
}
```

---

## 1. 企业注册接口（前端用户注册）

**POST** `/api/v1/auth/register`

请求体（包含新增的详细信息字段）：
```json
{
  "email": "<EMAIL>",
  "password": "password123",
  "userName": "张三",
  "companyName": {
    "zh-CN": "环球农化股份有限公司",
    "en": "Global Agrochem Inc.",
    "es": "Global Agrochem S.A."
  },
  "companyType": "supplier",
  "country": "cn",
  "businessCategories": ["pesticide_supplier", "fertilizer_supplier"],
  "businessScope": {
    "zh-CN": "专业从事农药、化肥等农化产品的研发、生产和销售",
    "en": "Professional in R&D, production and sales of pesticides, fertilizers and other agrochemical products",
    "es": "Profesional en I+D, producción y ventas de pesticidas, fertilizantes y otros productos agroquímicos"
  },
  "companySize": "medium",
  "mainProducts": {
    "zh-CN": "除草剂、杀虫剂、杀菌剂、植物生长调节剂",
    "en": "Herbicides, Insecticides, Fungicides, Plant Growth Regulators",
    "es": "Herbicidas, Insecticidas, Fungicidas, Reguladores del Crecimiento de Plantas"
  },
  "mainSuppliers": {
    "zh-CN": "中化集团、先正达、拜耳作物科学",
    "en": "ChemChina, Syngenta, Bayer Crop Science",
    "es": "ChemChina, Syngenta, Bayer Crop Science"
  },
  "annualImportExportValue": 5000000.00,
  "registrationNumber": "REG123456789",
  "taxNumber": "TAX987654321",
  "businessLicenseUrl": "https://example.com/business-license.jpg",
  "companyPhotosUrls": [
    "https://example.com/office1.jpg",
    "https://example.com/factory1.jpg"
  ]
}
```

响应：
```json
{
  "message": "Registration successful. Please wait for approval."
}
```

## 字段说明

### 新增字段详解

| 字段名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| `email` | string | 否 | 企业邮箱 | "<EMAIL>" |
| `country` | string | 否 | 国家代码 | "cn", "us", "de" |
| `businessCategories` | string[] | 否 | 业务类别代码列表 | ["pesticide_supplier", "fertilizer_supplier"] |
| `businessScope` | MultiLangText | 否 | 业务范围描述（多语言） | 见上方示例 |
| `companySize` | enum | 否 | 公司规模 | "startup", "small", "medium", "large", "enterprise" |
| `mainProducts` | MultiLangText | 否 | 主要产品/采购产品（多语言） | 见上方示例 |
| `mainSuppliers` | MultiLangText | 否 | 主要供应商（采购商填写，多语言） | 见上方示例 |
| `annualImportExportValue` | number | 否 | 年进口/出口额（美元） | 5000000.00 |
| `registrationNumber` | string | 否 | 注册证号 | "REG123456789" |
| `taxNumber` | string | 否 | 税号 | "TAX987654321" |
| `businessLicenseUrl` | string | 否 | 营业执照图片地址 | "https://example.com/license.jpg" |
| `companyPhotosUrls` | string[] | 否 | 公司照片地址列表 | ["url1.jpg", "url2.jpg"] |

### 公司规模枚举值

| 值 | 说明 | 人员规模 |
|----|------|----------|
| `startup` | 初创企业 | 1-10人 |
| `small` | 小型企业 | 11-50人 |
| `medium` | 中型企业 | 51-200人 |
| `large` | 大型企业 | 201-1000人 |
| `enterprise` | 大型集团 | 1000+人 |

---

## 2. 获取用户资料接口（查看扩展信息）

**GET** `/api/v1/auth/profile`

Header:
```
Authorization: Bearer {user_access_token}
```

响应示例（包含所有新增字段）：
```json
{
  "id": 1,
  "email": "<EMAIL>",
  "name": "张三",
  "role": "owner",
  "lastLoginAt": "2025-01-13T12:00:00.000+08:00",
  "type": "user",
  "company": {
    "id": 1,
    "name": {
      "zh-CN": "环球农化股份有限公司",
      "en": "Global Agrochem Inc.",
      "es": "Global Agrochem S.A."
    },
    "type": "supplier",
    "status": "active",
    "profile": {
      "description": {
        "zh-CN": "专业农化产品供应商",
        "en": "Professional agrochemical supplier"
      },
      "address": "北京市朝阳区",
      "phone": "************"
    },
    "rating": 4.5,
    "isTop100": false,
    "country": "cn",
    "businessCategories": ["pesticide_supplier", "fertilizer_supplier"],
    "businessScope": {
      "zh-CN": "专业从事农药、化肥等农化产品的研发、生产和销售",
      "en": "Professional in R&D, production and sales of pesticides, fertilizers and other agrochemical products",
      "es": "Profesional en I+D, producción y ventas de pesticidas, fertilizantes y otros productos agroquímicos"
    },
    "companySize": "medium",
    "mainProducts": {
      "zh-CN": "除草剂、杀虫剂、杀菌剂、植物生长调节剂",
      "en": "Herbicides, Insecticides, Fungicides, Plant Growth Regulators",
      "es": "Herbicidas, Insecticidas, Fungicidas, Reguladores del Crecimiento de Plantas"
    },
    "mainSuppliers": {
      "zh-CN": "中化集团、先正达、拜耳作物科学",
      "en": "ChemChina, Syngenta, Bayer Crop Science",
      "es": "ChemChina, Syngenta, Bayer Crop Science"
    },
    "annualImportExportValue": 5000000.00,
    "registrationNumber": "REG123456789",
    "taxNumber": "TAX987654321",
    "businessLicenseUrl": "https://example.com/business-license.jpg",
    "companyPhotosUrls": [
      "https://example.com/office1.jpg",
      "https://example.com/factory1.jpg"
    ]
  }
}
```

---

## 3. 管理员企业管理接口

### 3.1 获取待审核企业列表

**GET** `/api/v1/admin/companies/pending?page=1&limit=20`

响应示例：
```json
{
  "data": [
    {
      "id": 1,
      "name": {
        "zh-CN": "环球农化股份有限公司",
        "en": "Global Agrochem Inc."
      },
      "type": "supplier",
      "status": "pending_review",
      "country": "cn",
      "companySize": "medium",
      "annualImportExportValue": 5000000.00,
      "createdAt": "2025-01-13T10:30:00+08:00",
      "users": []
    }
  ],
  "meta": {
    "totalItems": 8,
    "itemCount": 8,
    "itemsPerPage": 20,
    "totalPages": 1,
    "currentPage": 1
  }
}
```

### 3.2 获取企业详情（包含所有扩展信息）

**GET** `/api/v1/admin/companies/1`

响应示例：
```json
{
  "id": 1,
  "name": {
    "zh-CN": "环球农化股份有限公司",
    "en": "Global Agrochem Inc.",
    "es": "Global Agrochem S.A."
  },
  "type": "supplier",
  "status": "active",
  "profile": {
    "description": {
      "zh-CN": "专业农药供应商",
      "en": "Professional pesticide supplier"
    },
    "address": "北京市朝阳区",
    "phone": "************"
  },
  "rating": 4.5,
  "isTop100": false,
  "country": "cn",
  "businessCategories": ["pesticide_supplier", "fertilizer_supplier"],
  "businessScope": {
    "zh-CN": "专业从事农药、化肥等农化产品的研发、生产和销售",
    "en": "Professional in R&D, production and sales of pesticides, fertilizers and other agrochemical products"
  },
  "companySize": "medium",
  "mainProducts": {
    "zh-CN": "除草剂、杀虫剂、杀菌剂、植物生长调节剂",
    "en": "Herbicides, Insecticides, Fungicides, Plant Growth Regulators"
  },
  "mainSuppliers": {
    "zh-CN": "中化集团、先正达、拜耳作物科学",
    "en": "ChemChina, Syngenta, Bayer Crop Science"
  },
  "annualImportExportValue": 5000000.00,
  "registrationNumber": "REG123456789",
  "taxNumber": "TAX987654321",
  "businessLicenseUrl": "https://example.com/business-license.jpg",
  "companyPhotosUrls": [
    "https://example.com/office1.jpg",
    "https://example.com/factory1.jpg"
  ],
  "users": [
    {
      "id": 1,
      "name": "张三",
      "email": "<EMAIL>",
      "role": "owner"
    }
  ],
  "subscriptions": [],
  "createdAt": "2025-01-13T10:30:00+08:00",
  "updatedAt": "2025-01-13T10:30:00+08:00"
}
```

### 3.3 审核企业

**POST** `/api/v1/admin/companies/1/review`

请求体：
```json
{
  "approved": true,
  "reason": "企业资质齐全，符合平台要求"
}
```

响应：
```json
{
  "id": 1,
  "name": {
    "zh-CN": "环球农化股份有限公司",
    "en": "Global Agrochem Inc."
  },
  "status": "active"
}
```

### 3.4 获取所有企业列表（支持筛选）

**GET** `/api/v1/admin/companies?page=1&limit=20&status=active&type=supplier&search=农化&country=cn&companySize=medium`

查询参数：
- `page`: 页码
- `limit`: 每页条数
- `status`: 企业状态 (pending_review, active, disabled)
- `type`: 企业类型 (buyer, supplier)
- `search`: 搜索关键词
- `country`: 国家代码筛选
- `companySize`: 公司规模筛选

### 3.5 创建企业（管理员）

**POST** `/api/v1/admin/companies`

请求体（包含所有扩展字段）：
```json
{
  "name": {
    "zh-CN": "测试企业1",
    "en": "test company 1"
  },
  "type": "supplier",
  "status": "active",
  "profile": {
    "description": {
      "zh-CN": "测试企业描述",
      "en": "test company description"
    },
    "address": "江苏省南京市江宁区麒麟街道智汇路186号启迪城智园",
    "phone": "***********",
    "website": "http://www.example.com"
  },
  "rating": 3,
  "isTop100": false,
  "email": "<EMAIL>",
  "country": "gb",
  "businessCategories": ["formulation_production", "domestic_trade"],
  "businessScope": {
    "zh-CN": "业务范围很广",
    "en": "Wide business scope"
  },
  "companySize": "small",
  "mainProducts": {
    "zh-CN": "主要产品",
    "en": "main products"
  },
  "annualImportExportValue": 10,
  "registrationNumber": "12345678",
  "taxNumber": "87654321",
  "businessLicenseUrl": "https://argochainhub.tos-cn-shanghai.volces.com/company_certificate/1/1752411674509-582.png",
  "companyPhotosUrls": [
    "https://argochainhub.tos-cn-shanghai.volces.com/company_certificate/1/1752411678502-800.png"
  ]
}
```

响应示例：
```json
{
  "id": "10",
  "createdAt": "2025-07-13T05:20:41.237Z",
  "updatedAt": "2025-07-13T05:20:41.237Z",
  "deletedAt": null,
  "name": {
    "zh-CN": "测试企业1",
    "en": "test company 1"
  },
  "type": "supplier",
  "status": "active",
  "profile": {
    "description": {
      "zh-CN": "测试企业描述",
      "en": "test company description"
    },
    "address": "江苏省南京市江宁区麒麟街道智汇路186号启迪城智园",
    "phone": "***********",
    "website": "http://www.example.com"
  },
  "rating": 3,
  "isTop100": false,
  "email": "<EMAIL>",
  "country": "gb",
  "businessCategories": ["formulation_production", "domestic_trade"],
  "businessScope": {
    "zh-CN": "业务范围很广",
    "en": "Wide business scope"
  },
  "companySize": "small",
  "mainProducts": {
    "zh-CN": "主要产品",
    "en": "main products"
  },
  "mainSuppliers": null,
  "annualImportExportValue": 10,
  "registrationNumber": "12345678",
  "taxNumber": "87654321",
  "businessLicenseUrl": "https://argochainhub.tos-cn-shanghai.volces.com/company_certificate/1/1752411674509-582.png",
  "companyPhotosUrls": [
    "https://argochainhub.tos-cn-shanghai.volces.com/company_certificate/1/1752411678502-800.png"
  ]
}
```

### 3.6 更新企业信息（支持新字段）

**PUT** `/api/v1/admin/companies/1`

请求体：
```json
{
  "name": {
    "zh-CN": "更新的环球农化股份有限公司",
    "en": "Updated Global Agrochem Inc."
  },
  "country": "cn",
  "businessCategories": ["pesticide_supplier", "fertilizer_supplier", "seed_supplier"],
  "businessScope": {
    "zh-CN": "更新的业务范围描述",
    "en": "Updated business scope description"
  },
  "companySize": "large",
  "mainProducts": {
    "zh-CN": "更新的主要产品列表",
    "en": "Updated main products list"
  },
  "mainSuppliers": {
    "zh-CN": "更新的主要供应商信息",
    "en": "Updated main suppliers information"
  },
  "annualImportExportValue": 8000000.00,
  "registrationNumber": "REG123456789-UPDATED",
  "taxNumber": "TAX987654321-UPDATED",
  "businessLicenseUrl": "https://example.com/updated-license.jpg",
  "companyPhotosUrls": [
    "https://example.com/new-office1.jpg",
    "https://example.com/new-factory1.jpg",
    "https://example.com/new-workshop1.jpg"
  ]
}
```

**重要提醒**: mainSuppliers字段已添加到UpdateCompanyDto中，支持采购商填写主要供应商信息。

---

## 4. 数据库变更说明

### 新增字段在数据库中的存储

```sql
-- 查看企业表结构（包含新字段）
DESCRIBE companies;

-- 示例查询所有扩展字段
SELECT 
  id,
  name,
  country,
  businessCategories,
  businessScope,
  companySize,
  mainProducts,
  mainSuppliers,
  annualImportExportValue,
  registrationNumber,
  taxNumber,
  businessLicenseUrl,
  companyPhotosUrls,
  created_at,
  updated_at
FROM companies 
WHERE id = 1;
```

### 字段类型说明

- **JSON字段**: `businessCategories`, `businessScope`, `mainProducts`, `mainSuppliers`, `companyPhotosUrls`
- **VARCHAR字段**: `country`, `registrationNumber`, `taxNumber`, `businessLicenseUrl`
- **ENUM字段**: `companySize`
- **DECIMAL字段**: `annualImportExportValue`

---

## 5. 测试用例

### 5.1 完整注册流程测试

1. **用户注册** (包含所有新字段)
2. **管理员审核** (通过)
3. **用户登录** (获取扩展信息)
4. **查看资料** (验证所有字段)

### 5.2 字段验证测试

- **必填字段验证**: email, password, userName, companyName, companyType
- **可选字段验证**: 所有新增的扩展字段
- **数据格式验证**: URL格式、多语言格式、枚举值
- **字段长度验证**: VARCHAR字段长度限制

### 5.3 边界情况测试

- 空值处理测试
- 超长字符串测试
- 无效枚举值测试
- 无效URL格式测试
- 无效JSON格式测试

---

## 6. 环境配置

### 开发环境
- **后端服务URL**: http://localhost:3010
- **API基础路径**: http://localhost:3010/api/v1
- **数据库**: argochainhub (MySQL 8.0)
- **时区**: 东八区 (Asia/Shanghai)

### 数据库连接
```
Host: *************
Port: 3306
Username: root
Password: root
Database: argochainhub
```

---

## 7. 注意事项

1. **时区显示**: 所有时间字段已配置为东八区显示
2. **多语言支持**: 部分字段支持多语言，格式为 `{"zh-CN": "中文", "en": "English"}`
3. **文件上传**: 图片地址字段需要先通过文件上传接口获取URL
4. **数据验证**: 所有新字段都有相应的验证规则
5. **向下兼容**: 新字段为可选字段，不影响现有功能

---

## 8. 常见问题

### Q: 注册时哪些字段是必填的？
A: email, password, userName, companyName, companyType 为必填字段，其他扩展字段均为可选。

### Q: 公司规模枚举值有哪些？
A: startup(初创企业), small(小型企业), medium(中型企业), large(大型企业), enterprise(大型集团)

### Q: 业务类别代码从哪里获取？
A: 通过字典管理接口获取可用的业务类别代码列表

### Q: 多语言字段的格式要求？
A: 必须包含至少一种语言，推荐格式：`{"zh-CN": "中文", "en": "English"}`

### Q: 年进出口额的单位是什么？
A: 美元，存储为数字类型，支持小数点后2位